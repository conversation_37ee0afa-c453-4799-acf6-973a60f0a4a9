"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Mail, RefreshCw, Trash2, ChevronLeft, ChevronRight, Clock, Check, X } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { InvitationSend } from "@/lib/domains/invitation/invitation.types"
import { InvitationSendService } from "@/lib/domains/invitation/invitation-send.service"
import { ErrorBoundary } from "@/components/error-boundary"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIsMobile } from "@/hooks/use-mobile"

interface InvitationSendsListProps {
  invitationSends: InvitationSend[]
  squadId: string
}

export function InvitationSendsList({ invitationSends, squadId }: InvitationSendsListProps) {
  const [processingInvitations, setProcessingInvitations] = useState<Record<string, boolean>>({})
  const [activeTab, setActiveTab] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const isMobile = useIsMobile()
  const [invitationsPerPage] = useState(isMobile ? 3 : 5)

  // Format date safely
  const formatDate = (timestamp: any) => {
    if (!timestamp) return "Unknown"

    try {
      return typeof timestamp.toDate === "function"
        ? timestamp.toDate().toLocaleString()
        : new Date(timestamp).toLocaleString()
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  const handleResendInvitation = async (invitationSend: InvitationSend) => {
    if (!invitationSend.id) return

    setProcessingInvitations((prev) => ({ ...prev, [invitationSend.id!]: true }))

    try {
      // Check if user is already in squad before resending
      const { SquadService } = await import("@/lib/domains/squad/squad.service")
      const { UserService } = await import("@/lib/domains/user/user.service")

      // Get user by email to check squad membership
      const user = await UserService.getUserByEmail(invitationSend.email)

      if (user) {
        const isAlreadyMember = await SquadService.isUserSquadMember(user.uid, squadId)

        if (isAlreadyMember) {
          // User is already in squad - mark invitation as invalid
          await InvitationSendService.updateInvitationSendStatus(
            squadId,
            invitationSend.id,
            "invalid"
          )

          toast({
            title: "Cannot resend invitation",
            description: `${invitationSend.email} is already a member of this squad`,
            variant: "destructive",
          })
          return
        }
      }

      // Use the invitation service to resend
      const result = await InvitationSendService.resendInvitationSend(squadId, invitationSend.id)

      if (result.success) {
        toast({
          title: "Invitation resent",
          description: `Invitation resent to ${invitationSend.email}`,
        })
      } else {
        toast({
          title: "Failed to resend invitation",
          description: result.error || "An error occurred",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error resending invitation:", error)
      toast({
        title: "Failed to resend invitation",
        description: "An error occurred while resending the invitation",
        variant: "destructive",
      })
    } finally {
      setProcessingInvitations((prev) => ({ ...prev, [invitationSend.id!]: false }))
    }
  }

  const handleDeleteInvitation = async (invitationSend: InvitationSend) => {
    if (!invitationSend.id) return

    setProcessingInvitations((prev) => ({ ...prev, [invitationSend.id!]: true }))

    try {
      const result = await InvitationSendService.deleteInvitationSend(squadId, invitationSend.id)

      if (result.success) {
        toast({
          title: "Invitation deleted",
          description: `Invitation to ${invitationSend.email} has been deleted`,
        })
      } else {
        toast({
          title: "Failed to delete invitation",
          description: result.error || "An error occurred",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting invitation:", error)
      toast({
        title: "Failed to delete invitation",
        description: "An error occurred while deleting the invitation",
        variant: "destructive",
      })
    } finally {
      setProcessingInvitations((prev) => ({ ...prev, [invitationSend.id!]: false }))
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "sent":
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-200">
            <Clock className="w-3 h-3 mr-1" />
            Sent
          </Badge>
        )
      case "accepted":
        return (
          <Badge variant="outline" className="text-green-600 border-green-200">
            <Check className="w-3 h-3 mr-1" />
            Accepted
          </Badge>
        )
      case "rejected":
        return (
          <Badge variant="outline" className="text-red-600 border-red-200">
            <X className="w-3 h-3 mr-1" />
            Rejected
          </Badge>
        )
      case "invalid":
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-200">
            <X className="w-3 h-3 mr-1" />
            Invalid
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-200">
            {status}
          </Badge>
        )
    }
  }

  // Filter invitations based on active tab
  const filteredInvitations = invitationSends.filter((invitation) => {
    switch (activeTab) {
      case "pending":
        return invitation.status === "sent"
      case "accepted":
        return invitation.status === "accepted"
      case "rejected":
        return invitation.status === "rejected"
      default:
        return true
    }
  })

  // Pagination
  const totalPages = Math.ceil(filteredInvitations.length / invitationsPerPage)
  const startIndex = (currentPage - 1) * invitationsPerPage
  const endIndex = startIndex + invitationsPerPage
  const currentInvitations = filteredInvitations.slice(startIndex, endIndex)

  // Reset to page 1 when changing tabs
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setCurrentPage(1)
  }

  if (invitationSends.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Mail className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No invitations sent</h3>
          <p className="text-muted-foreground">
            Invitations you send will appear here. You can resend or manage them from this list.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All ({invitationSends.length})</TabsTrigger>
            <TabsTrigger value="pending">
              Pending ({invitationSends.filter((i) => i.status === "sent").length})
            </TabsTrigger>
            <TabsTrigger value="accepted">
              Accepted ({invitationSends.filter((i) => i.status === "accepted").length})
            </TabsTrigger>
            <TabsTrigger value="rejected">
              Rejected ({invitationSends.filter((i) => i.status === "rejected").length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {currentInvitations.map((invitationSend) => (
              <Card key={invitationSend.id} className="transition-shadow hover:shadow-md">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-sm font-medium text-foreground truncate">
                          {invitationSend.email}
                        </h4>
                        {getStatusBadge(invitationSend.status)}
                      </div>
                      <div className="text-xs text-muted-foreground space-y-1">
                        <p>Sent: {formatDate(invitationSend.sentAt)}</p>
                        {invitationSend.lastResent && (
                          <p>Last resent: {formatDate(invitationSend.lastResent)}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            {invitationSend.status === "sent" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleResendInvitation(invitationSend)}
                                disabled={processingInvitations[invitationSend.id!]}
                              >
                                {processingInvitations[invitationSend.id!] ? (
                                  <RefreshCw className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Mail className="w-4 h-4" />
                                )}
                                {!isMobile && <span className="ml-2">Resend</span>}
                              </Button>
                            )}
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {invitationSend.status === "accepted"
                                ? "Cannot resend - invitation already accepted"
                                : invitationSend.status === "rejected"
                                  ? "Cannot resend - invitation was rejected"
                                  : invitationSend.status === "invalid"
                                    ? "Cannot resend - invitation is invalid"
                                    : "Resend invitation email"}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteInvitation(invitationSend)}
                              disabled={processingInvitations[invitationSend.id!]}
                            >
                              <Trash2 className="w-4 h-4" />
                              {!isMobile && <span className="ml-2">Delete</span>}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete invitation</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Showing {startIndex + 1} to {Math.min(endIndex, filteredInvitations.length)} of{" "}
                  {filteredInvitations.length} invitations
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="w-4 h-4" />
                    Previous
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </ErrorBoundary>
  )
}
